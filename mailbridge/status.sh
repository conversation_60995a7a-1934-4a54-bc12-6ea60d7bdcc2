#!/bin/bash

# PROTON Mail Bridge + PMG Status Check
# =====================================

# Lade Konfiguration
source "$(dirname "$0")/.env"

echo "📊 PMG → PROTON Bridge Status"
echo "============================="

# PMG Status
echo "🔧 PMG Status:"
if nc -z "$PMG_IP" 8006; then
    echo "✅ PMG Web-Interface ($PMG_IP:8006) OK"
else
    echo "❌ PMG Web-Interface Problem"
fi

if nc -z "$PMG_IP" "$PMG_INTERNAL_PORT"; then
    echo "✅ PMG SMTP ($PMG_IP:$PMG_INTERNAL_PORT) OK"
else
    echo "❌ PMG SMTP Problem"
fi

echo ""
echo "🐳 Bridge Container Status:"
docker compose ps

echo ""
echo "🌐 Bridge Port Tests:"
nc -z "$CONTAINER_IP" "$BRIDGE_IMAP_PORT" && echo "✅ Bridge IMAP ($CONTAINER_IP:$BRIDGE_IMAP_PORT) OK" || echo "❌ Bridge IMAP Problem"
nc -z "$CONTAINER_IP" "$BRIDGE_SMTP_PORT" && echo "✅ Bridge SMTP ($CONTAINER_IP:$BRIDGE_SMTP_PORT) OK" || echo "❌ Bridge SMTP Problem"

echo ""
echo "📧 Mail-Client Einstellungen (über PMG):"
echo "IMAP: $PMG_IP:143"
echo "SMTP: $PMG_IP:$PMG_INTERNAL_PORT"
echo "User: <EMAIL>"
echo "Pass: a4a_n2Ri1At22FsQ9jjPFA"

echo ""
echo "🔗 Mail-Flow:"
echo "Client → PMG ($PMG_IP) → Bridge ($CONTAINER_IP) → PROTON"

echo ""
echo "📝 Bridge Logs:"
docker compose logs --tail=5 proton-bridge
