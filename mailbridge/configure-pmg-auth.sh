#!/bin/bash

# PMG SMTP Authentifizierung für PROTON Bridge konfigurieren
# ==========================================================

echo "🔧 PMG SMTP Authentifizierung konfigurieren"
echo "==========================================="

# Bridge Zugangsdaten
BRIDGE_IP="***********"
BRIDGE_PORT="1025"
BRIDGE_USER="<EMAIL>"
BRIDGE_PASS="a4a_n2Ri1At22FsQ9jjPFA"

echo "Bridge: $BRIDGE_IP:$BRIDGE_PORT"
echo "User: $BRIDGE_USER"
echo ""

# SSH zu PMG und konfiguriere Authentifizierung
ssh root@*********** << EOF
echo "🔧 Konfiguriere PMG SMTP Authentifizierung..."

# 1. Erstelle SASL Password Map
echo "Erstelle SASL Password Map..."
echo "[$BRIDGE_IP]:$BRIDGE_PORT $BRIDGE_USER:$BRIDGE_PASS" > /etc/postfix/sasl_passwd

# 2. Erstelle Hash-Datei
echo "Erstelle Postfix Hash..."
postmap /etc/postfix/sasl_passwd

# 3. Sichere Berechtigungen
chmod 600 /etc/postfix/sasl_passwd*
chown root:root /etc/postfix/sasl_passwd*

# 4. Aktualisiere Postfix main.cf
echo "Aktualisiere Postfix Konfiguration..."

# Backup der main.cf
cp /etc/postfix/main.cf /etc/postfix/main.cf.backup

# Füge SASL Konfiguration hinzu
cat >> /etc/postfix/main.cf << 'POSTFIX_EOF'

# PROTON Bridge SMTP Authentication
smtp_sasl_auth_enable = yes
smtp_sasl_password_maps = hash:/etc/postfix/sasl_passwd
smtp_sasl_security_options = noanonymous
smtp_sasl_mechanism_filter = plain, login
smtp_use_tls = no
smtp_enforce_tls = no
POSTFIX_EOF

# 5. Postfix neu laden
echo "Lade Postfix Konfiguration neu..."
postfix reload

echo "✅ PMG SMTP Authentifizierung konfiguriert!"
echo ""
echo "Konfiguration:"
echo "- SASL Auth: aktiviert"
echo "- Password Map: /etc/postfix/sasl_passwd"
echo "- Bridge: [$BRIDGE_IP]:$BRIDGE_PORT"
echo "- User: $BRIDGE_USER"

EOF

echo ""
echo "🎉 PMG Authentifizierung konfiguriert!"
echo ""
echo "Jetzt testen Sie:"
echo "1. Mail von anderem PC: nc *********** 25"
echo "2. Oder: ./quick-mail-test.sh"
