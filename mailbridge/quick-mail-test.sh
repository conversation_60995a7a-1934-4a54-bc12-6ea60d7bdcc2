#!/bin/bash

# <PERSON><PERSON><PERSON> PMG Mail Test
# =======================

PMG_IP="***********"
FROM="<EMAIL>"
TO="<EMAIL>"  # Verwende nk-it.cloud Domain für Test

echo "📧 Schneller PMG Mail Test"
echo "=========================="
echo "PMG: $PMG_IP:25"
echo "Von: $FROM → An: $TO"
echo ""

# Einfache Test-Mail mit netcat
echo "Sende Test-Mail..."

{
    echo "HELO nk-it.cloud"
    sleep 1
    echo "MAIL FROM:<$FROM>"
    sleep 1
    echo "RCPT TO:<$TO>"
    sleep 1
    echo "DATA"
    sleep 1
    echo "From: $FROM"
    echo "To: $TO"
    echo "Subject: PMG Test $(date '+%H:%M:%S')"
    echo "Date: $(date -R)"
    echo ""
    echo "Test-Mail über PMG → PROTON Bridge"
    echo "Gesendet: $(date)"
    echo "Von: $(hostname)"
    echo ""
    echo "Mail-Flow Test erfolgreich!"
    echo "."
    sleep 1
    echo "QUIT"
} | nc "$PMG_IP" 25

echo ""
echo "✅ Test-Mail gesendet!"
echo "📧 Prüfen Sie Ihren Posteingang: $TO"
