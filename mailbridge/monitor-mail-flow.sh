#!/bin/bash

# PMG → Bridge Mail-Flow Live Monitor
# ===================================

PMG_IP="***********"
BRIDGE_IP="***********"

echo "📊 PMG → Bridge Mail-Flow Live Monitor"
echo "======================================"
echo "PMG: $PMG_IP"
echo "Bridge: $BRIDGE_IP"
echo "Drücken Sie CTRL+C zum Beenden"
echo ""

# Funktion für Live-Monitoring
monitor_connections() {
    while true; do
        clear
        echo "📊 Live Mail-Flow Monitor - $(date)"
        echo "=================================="
        
        # PMG Verbindungen
        echo "🔧 PMG SMTP Verbindungen ($PMG_IP:25):"
        netstat -an | grep "$PMG_IP:25" | head -5 || echo "Keine aktiven PMG Verbindungen"
        
        echo ""
        echo "🌉 Bridge SMTP Verbindungen ($BRIDGE_IP:1025):"
        netstat -an | grep "$BRIDGE_IP:1025" | head -5 || echo "Keine aktiven Bridge SMTP Verbindungen"
        
        echo ""
        echo "📧 Bridge IMAP Verbindungen ($BRIDGE_IP:1143):"
        netstat -an | grep "$BRIDGE_IP:1143" | head -5 || echo "Keine aktiven Bridge IMAP Verbindungen"
        
        echo ""
        echo "🐳 Docker Bridge Status:"
        docker compose ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || echo "Docker nicht verfügbar"
        
        echo ""
        echo "📈 Netzwerk-Statistiken:"
        echo "PMG Port 25: $(netstat -an | grep -c ":25.*ESTABLISHED") aktive Verbindungen"
        echo "Bridge Port 1025: $(netstat -an | grep -c ":1025.*ESTABLISHED") aktive Verbindungen"
        echo "Bridge Port 1143: $(netstat -an | grep -c ":1143.*ESTABLISHED") aktive Verbindungen"
        
        echo ""
        echo "⏱️  Aktualisierung alle 3 Sekunden... (CTRL+C zum Beenden)"
        
        sleep 3
    done
}

# Trap für sauberes Beenden
trap 'echo -e "\n\n👋 Monitoring beendet"; exit 0' INT

# Monitoring starten
monitor_connections
