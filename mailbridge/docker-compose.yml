services:
  # Hauptvariante: Offizielle deb-basierte Version (amd64)
  proton-bridge:
    image: shenxn/protonmail-bridge:latest  # deb-basiert, nur amd64
    container_name: proton-bridge
    restart: unless-stopped

    environment:
      - PROTONMAIL_USERNAME=${PROTON_USERNAME}
      - PROTONMAIL_PASSWORD=${PROTON_PASSWORD}
      - BRIDGE_LOG_LEVEL=${BRIDGE_LOG_LEVEL:-info}

    ports:
      # PMG Integration - *********** → ***********
      - "${CONTAINER_IP}:${BRIDGE_IMAP_PORT}:143"   # IMAP für PMG
      - "${CONTAINER_IP}:${BRIDGE_SMTP_PORT}:25"    # SMTP für PMG (Port 1025)
      - "${CONTAINER_IP}:${BRIDGE_API_PORT}:8080"   # Bridge API

    volumes:
      - protonmail-data:/root
      - ./logs:/var/log/protonmail-bridge
      - /etc/localtime:/etc/localtime:ro

    networks:
      - mailbridge-network

    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "1143"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

    # Sicherheitseinstellungen
    security_opt:
      - no-new-privileges:true
    read_only: false
    tmpfs:
      - /tmp:noexec,nosuid,size=100m

  # Alternative: Build-basierte Version (Multi-Arch: amd64, arm64, arm/v7)
  proton-bridge-build:
    image: shenxn/protonmail-bridge:build  # Source-basiert, Multi-Arch
    container_name: proton-bridge-build
    restart: unless-stopped
    profiles:
      - build-version

    environment:
      - PROTONMAIL_USERNAME=${PROTON_USERNAME}
      - PROTONMAIL_PASSWORD=${PROTON_PASSWORD}
      - BRIDGE_LOG_LEVEL=${BRIDGE_LOG_LEVEL:-info}

    ports:
      - "${CONTAINER_IP}:${BRIDGE_IMAP_PORT}:143"
      - "${CONTAINER_IP}:${BRIDGE_SMTP_PORT}:25"
      - "${CONTAINER_IP}:${BRIDGE_API_PORT}:8080"

    volumes:
      - protonmail-data-build:/root
      - ./logs:/var/log/protonmail-bridge
      - /etc/localtime:/etc/localtime:ro

    networks:
      - mailbridge-network

  # Optional: Mail monitoring service
  mailhog:
    image: mailhog/mailhog:latest
    container_name: mailhog
    restart: unless-stopped
    ports:
      - "${CONTAINER_IP}:8025:8025"  # Web UI
      - "${CONTAINER_IP}:1025:1025"  # SMTP (alternative)
    networks:
      - mailbridge-network
    profiles:
      - monitoring

networks:
  mailbridge-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  protonmail-data:
    driver: local
  protonmail-data-build:
    driver: local
  bridge-config:
    driver: local
