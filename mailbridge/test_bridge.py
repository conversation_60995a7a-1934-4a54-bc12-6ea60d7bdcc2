#!/usr/bin/env python3
"""
ProtonMail Bridge Test Script
Sendet eine Test-E-Mail über die ProtonMail Bridge
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime
import uuid
from email.utils import formatdate, make_msgid

# Bridge-Konfiguration (über PMG → Bridge ohne Auth)
SMTP_SERVER = "***********"  # PMG Gateway
SMTP_PORT = 1025
USERNAME = "<EMAIL>"
PASSWORD = "a4a_n2Ri1At22FsQ9jjPFA"

# E-Mail-Details
FROM_EMAIL = "<EMAIL>"
TO_EMAIL = "<EMAIL>"
SUBJECT = "Test Mail - PMG zu ProtonMail"

def create_test_email():
    """Erstellt eine ordentliche Test-E-Mail mit korrekten Headern"""

    # Einfacher, sauberer E-Mail-Inhalt (ASCII-kompatibel)
    body = f"""Hallo,

dies ist eine Test-E-Mail vom nk-it.cloud Mail-System.

Von: {FROM_EMAIL}
An: {TO_EMAIL}
Datum: {datetime.now().strftime('%d.%m.%Y %H:%M:%S')}

Die E-Mail wird ueber PMG gefiltert und weitergeleitet.

Mit freundlichen Gruessen
nk-it.cloud System
"""

    # E-Mail-Objekt erstellen
    msg = MIMEText(body, 'plain', 'utf-8')

    # Korrekte E-Mail-Header setzen
    msg['From'] = FROM_EMAIL
    msg['To'] = TO_EMAIL
    msg['Subject'] = SUBJECT
    msg['Date'] = formatdate(localtime=True)
    msg['Message-ID'] = make_msgid(domain="nk-it.cloud")
    msg['X-Mailer'] = "ProtonMail Bridge Test Script"

    # Content-Transfer-Encoding auf 7bit setzen (nicht base64)
    del msg['Content-Transfer-Encoding']
    msg['Content-Transfer-Encoding'] = '7bit'
    
    # Einfacher, sauberer E-Mail-Inhalt (ASCII-kompatibel)
    body = f"""Hallo,

dies ist eine Test-E-Mail vom nk-it.cloud Mail-System.

Von: {FROM_EMAIL}
An: {TO_EMAIL}
Datum: {datetime.now().strftime('%d.%m.%Y %H:%M:%S')}

Die E-Mail wird ueber PMG gefiltert und weitergeleitet.

Mit freundlichen Gruessen
nk-it.cloud System
"""

    # Inhalt setzen
    msg.set_payload(body)
    return msg

def send_email():
    """Sendet die Test-E-Mail über die Bridge"""
    
    try:
        print("🚀 Erstelle Test-E-Mail...")
        msg = create_test_email()
        
        print(f"📧 Sende E-Mail von {FROM_EMAIL} an {TO_EMAIL}...")
        print(f"📡 SMTP Server: {SMTP_SERVER}:{SMTP_PORT}")
        print(f"👤 Username: {USERNAME}")
        
        # SMTP-Verbindung aufbauen
        print("🔗 Verbinde mit SMTP Server...")
        server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)

        # Debug-Modus aktivieren
        server.set_debuglevel(1)

        # Prüfe ob STARTTLS verfügbar ist
        if server.has_extn('STARTTLS'):
            print("🔒 Aktiviere STARTTLS...")
            server.starttls()
        else:
            print("⚠️ STARTTLS nicht verfügbar, verwende unverschlüsselte Verbindung...")

        # IMMER Authentifizierung - Mail funktioniert nur mit Username/Password!
        print("🔑 Authentifizierung...")
        server.login(USERNAME, PASSWORD)
        
        # E-Mail senden
        print("📤 Sende E-Mail...")
        text = msg.as_string().encode('utf-8')
        server.sendmail(FROM_EMAIL, TO_EMAIL, text)
        
        # Verbindung schließen
        server.quit()
        
        print("✅ E-Mail erfolgreich gesendet!")
        print(f"📬 Von: {FROM_EMAIL}")
        print(f"📮 An: {TO_EMAIL}")
        print(f"📋 Betreff: {SUBJECT}")
        
    except Exception as e:
        print(f"❌ Fehler beim Senden der E-Mail: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 PROTONMAIL BRIDGE TEST")
    print("=" * 60)
    
    success = send_email()
    
    print("=" * 60)
    if success:
        print("🎉 TEST ERFOLGREICH! Bridge funktioniert einwandfrei!")
    else:
        print("💥 TEST FEHLGESCHLAGEN! Bitte Konfiguration prüfen.")
    print("=" * 60)
