#!/usr/bin/env python3
"""
ProtonMail Bridge Test Script
Sendet eine Test-E-Mail über die ProtonMail Bridge
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime

# Bridge-Konfiguration (direkt zur Bridge)
SMTP_SERVER = "***********"  # ProtonMail Bridge direkt
SMTP_PORT = 1025
USERNAME = "<EMAIL>"
PASSWORD = "a4a_n2Ri1At22FsQ9jjPFA"

# E-Mail-Details
FROM_EMAIL = "<EMAIL>"
TO_EMAIL = "<EMAIL>"
SUBJECT = "ProtonMail Bridge Test - Erfolgreich konfiguriert!"

def create_test_email():
    """Erstellt die Test-E-Mail mit Bridge-Konfigurationsdaten"""
    
    # E-Mail-Objekt erstellen
    msg = MIMEMultipart()
    msg['From'] = FROM_EMAIL
    msg['To'] = TO_EMAIL
    msg['Subject'] = SUBJECT
    
    # E-Mail-Inhalt
    body = f"""
Hallo!

Dies ist eine Test-E-Mail von der ProtonMail Bridge Konfiguration.

🎉 BRIDGE ERFOLGREICH KONFIGURIERT! 🎉

Hier sind die aktuellen Bridge-Konfigurationsdaten:

=== IMAP EINSTELLUNGEN ===
Address:   127.0.0.1 (intern) / *********** (extern)
IMAP port: 1143
Username:  {USERNAME}
Password:  {PASSWORD}
Security:  STARTTLS

=== SMTP EINSTELLUNGEN ===
Address:   127.0.0.1 (intern) / *********** (extern)
SMTP port: 1025
Username:  {USERNAME}
Password:  {PASSWORD}
Security:  STARTTLS

=== ÜBER PMG GATEWAY ===
PMG IMAP:  ***********:143
PMG SMTP:  ***********:25
Filtering: Spam/Virus-Schutz aktiv

=== VERFÜGBARE E-MAIL-ADRESSEN ===
✅ <EMAIL> (Standard)
✅ <EMAIL>
✅ <EMAIL> (diese E-Mail)
✅ <EMAIL>
✅ <EMAIL>

Alle Adressen verwenden dieselben Zugangsdaten!

=== SYSTEM-INFO ===
Gesendet: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Von: {FROM_EMAIL}
Docker: shenxn/protonmail-bridge:latest
PMG: ***********
Bridge: ***********

Die Bridge funktioniert einwandfrei! 🚀

Beste Grüße,
nk-it.cloud ProtonMail Bridge System
"""
    
    msg.attach(MIMEText(body, 'plain', 'utf-8'))
    return msg

def send_email():
    """Sendet die Test-E-Mail über die Bridge"""
    
    try:
        print("🚀 Erstelle Test-E-Mail...")
        msg = create_test_email()
        
        print(f"📧 Sende E-Mail von {FROM_EMAIL} an {TO_EMAIL}...")
        print(f"📡 SMTP Server: {SMTP_SERVER}:{SMTP_PORT}")
        print(f"👤 Username: {USERNAME}")
        
        # SMTP-Verbindung aufbauen
        print("🔗 Verbinde mit SMTP Server...")
        server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)

        # Debug-Modus aktivieren
        server.set_debuglevel(1)

        # Prüfe ob STARTTLS verfügbar ist
        if server.has_extn('STARTTLS'):
            print("🔒 Aktiviere STARTTLS...")
            server.starttls()
        else:
            print("⚠️ STARTTLS nicht verfügbar, verwende unverschlüsselte Verbindung...")

        # Anmelden (Bridge erfordert Authentifizierung)
        print("🔑 Authentifizierung...")
        server.login(USERNAME, PASSWORD)
        
        # E-Mail senden
        print("📤 Sende E-Mail...")
        text = msg.as_string()
        server.sendmail(FROM_EMAIL, TO_EMAIL, text)
        
        # Verbindung schließen
        server.quit()
        
        print("✅ E-Mail erfolgreich gesendet!")
        print(f"📬 Von: {FROM_EMAIL}")
        print(f"📮 An: {TO_EMAIL}")
        print(f"📋 Betreff: {SUBJECT}")
        
    except Exception as e:
        print(f"❌ Fehler beim Senden der E-Mail: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 PROTONMAIL BRIDGE TEST")
    print("=" * 60)
    
    success = send_email()
    
    print("=" * 60)
    if success:
        print("🎉 TEST ERFOLGREICH! Bridge funktioniert einwandfrei!")
    else:
        print("💥 TEST FEHLGESCHLAGEN! Bitte Konfiguration prüfen.")
    print("=" * 60)
