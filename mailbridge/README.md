# PROTON Mail Bridge für nk-it.cloud

**<PERSON><PERSON><PERSON>, funktionierende Docker-Lösung für PROTON Mail IMAP/SMTP Bridge**

## 📋 Übersicht

Diese Docker-Installation ermöglicht es, alle Ihre PROTON Mail Adressen über IMAP/SMTP in lokalen Mail-Clients zu verwenden:

- ✅ **<EMAIL>** (Standard)
- ✅ **<EMAIL>**
- ✅ **<EMAIL>**
- ✅ **<EMAIL>**
- ✅ **<EMAIL>**

## 🏗️ Setup mit PMG Integration

```
┌─────────────────┐    ┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Mail Client   │───▶│  PMG Gateway    │───▶│  PROTON Bridge   │───▶│  PROTON Mail    │
│  (Thunderbird,  │    │ 10.0.88.202     │    │   (Docker)       │    │   (Internet)    │
│   Outlook, etc) │    │ Spam/Virus      │    │  10.0.88.200     │    │                 │
└─────────────────┘    │ Filtering       │    └──────────────────┘    └─────────────────┘
                       └─────────────────┘
```

## 🚀 Installation (3 einfache Schritte)

### 1. E<PERSON><PERSON><PERSON> Einrichtung (ein<PERSON>ig)

```bash
# Container stoppen (falls l<PERSON><PERSON>t)
docker compose down

# Authentifizierung durchführen
docker compose run --rm proton-bridge init
```

**In der interaktiven Shell:**
1. `login` eingeben
2. Ihre PROTON E-Mail: `<EMAIL>`
3. Ihr PROTON Passwort eingeben
4. 2FA Code eingeben
5. `info` eingeben (zeigt Bridge-Zugangsdaten)
6. `exit` eingeben

### 2. Service starten

```bash
# Bridge starten
docker compose up -d
```

### 3. Fertig!

```bash
# Status prüfen
docker compose ps

# Logs anzeigen
docker compose logs -f proton-bridge
```

## 📧 Mail-Client Konfiguration (über PMG)

**Verwenden Sie diese Einstellungen für ALLE Ihre E-Mail-Adressen:**

| Einstellung | Wert |
|-------------|------|
| **IMAP Server** | `10.0.88.202` (PMG) |
| **IMAP Port** | `143` |
| **SMTP Server** | `10.0.88.202` (PMG) |
| **SMTP Port** | `25` |
| **Username** | `<EMAIL>` |
| **Password** | `a4a_n2Ri1At22FsQ9jjPFA` |
| **Verschlüsselung** | STARTTLS |

### 🔑 Bridge-Zugangsdaten abrufen

**So erhalten Sie die aktuellen User/Password-Informationen:**

```bash
# In den Container gehen und Bridge-Info abrufen
docker exec -it proton-bridge bash
echo "info" > faketty

# Oder direkt die Logs verfolgen
docker compose logs -f proton-bridge
```

**Wichtig**: Alle Ihre E-Mail-Adressen verwenden **dieselben Zugangsdaten**:
- ✅ <EMAIL>
- ✅ <EMAIL>
- ✅ <EMAIL>
- ✅ <EMAIL>
- ✅ <EMAIL>

**Vorteile durch PMG:**
- ✅ Spam-Filterung vor PROTON Bridge
- ✅ Virus-Scanning aller E-Mails
- ✅ Quarantine-Management
- ✅ Zentrale Mail-Logs

### Thunderbird Setup

1. **Konto hinzufügen** → Manuelle Konfiguration
2. Obige Einstellungen eingeben
3. **Identitäten erstellen** für jede E-Mail-Adresse:
   - <EMAIL>
   - <EMAIL>
   - <EMAIL>

### Outlook Setup

1. **Datei** → **Konto hinzufügen** → **Manuelle Einrichtung**
2. **IMAP/POP** auswählen
3. Obige Einstellungen eingeben

## 🔧 Verwaltung

### Service-Befehle

```bash
# Status prüfen
docker compose ps

# Logs anzeigen
docker compose logs -f proton-bridge

# Service stoppen
docker compose down

# Service starten
docker compose up -d

# Service neu starten
docker compose restart proton-bridge
```

### Bei Problemen

```bash
# Container-Status prüfen
docker compose ps

# Detaillierte Logs
docker compose logs proton-bridge

# Container neu erstellen
docker compose down
docker compose up -d

# Erneute Authentifizierung (falls nötig)
docker compose run --rm proton-bridge init
```

## 🧪 Tests

### Verbindung testen

```bash
# IMAP Test
nc -z 10.0.88.200 1143 && echo "✅ IMAP OK" || echo "❌ IMAP Problem"

# SMTP Test
nc -z 10.0.88.200 1025 && echo "✅ SMTP OK" || echo "❌ SMTP Problem"

# Container Status
docker compose ps
```

## 📁 Wichtige Dateien

```
/opt/mailbridge/
├── .env                    # Umgebungsvariablen
├── docker-compose.yml      # Docker Konfiguration
├── README.md              # Diese Anleitung
└── logs/                  # Log-Dateien
```

## � Docker Image Information

**Verwendetes Image**: `shenxn/protonmail-bridge:latest`

- ✅ **Vertrauenswürdig**: 510+ GitHub Stars, Millionen Downloads
- ✅ **Community Standard**: Beliebtestes ProtonMail Bridge Docker Image
- ✅ **Aktiv maintained**: Regelmäßige Updates
- ✅ **Multi-Platform**: Unterstützt AMD64, ARM64, ARM/v7
- 📖 **GitHub**: https://github.com/shenxn/protonmail-bridge-docker
- 🐳 **Docker Hub**: https://hub.docker.com/r/shenxn/protonmail-bridge

### Image Tags
- `latest`: Neueste stabile Version (basiert auf .deb Release)
- `build`: Neueste Version (aus Quellcode kompiliert)
- `[version]`: Spezifische Versionen

## �🔒 Sicherheit

- Bridge läuft isoliert im Docker Container
- Ports nur für lokales Netzwerk (10.0.88.0/24) zugänglich
- Authentifizierung über PROTON 2FA
- Automatische Verschlüsselung (STARTTLS)
- Vertrauenswürdiges Community-Image mit transparentem Build-Prozess

## ❓ Häufige Fragen

**Q: Kann ich von allen meinen E-Mail-Adressen versenden?**
A: Ja! Verwenden Sie in Ihrem Mail-Client einfach das "Von"-Feld oder richten Sie Identitäten ein.

**Q: Was passiert bei einem Container-Neustart?**
A: Die Bridge startet automatisch neu. Die Authentifizierung bleibt gespeichert.

**Q: Kann ich mehrere Mail-Clients gleichzeitig verwenden?**
A: Ja, aber PROTON Bridge hat Limits für gleichzeitige Verbindungen.

**Q: Wie aktualisiere ich die Bridge?**
A: `docker compose pull && docker compose up -d`

**Q: Wie bekomme ich die aktuellen User/Password-Informationen?**
A: `docker exec -it proton-bridge bash` → `echo "info" > faketty` oder `docker compose logs -f proton-bridge`

**Q: Warum zeigt der Container "unhealthy" Status?**
A: Das ist normal während/nach dem ersten Sync. Solange die Logs "Sync finished" zeigen, funktioniert alles.

---

## 📄 Lizenz

Dieses Setup ist für die interne Nutzung von nk-it.cloud bestimmt.
PROTON Mail Bridge unterliegt den Lizenzbedingungen von Proton Technologies AG.
