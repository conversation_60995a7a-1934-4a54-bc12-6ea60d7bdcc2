#!/bin/bash

# PMG → Bridge Diagnose
# =====================

PMG_IP="***********"
BRIDGE_IP="***********"

echo "🔍 PMG → Bridge Diagnose"
echo "========================"
echo ""

# Test 1: Direkte Bridge Verbindung
echo "🧪 Test 1: Direkte Bridge SMTP Verbindung"
echo "=========================================="
echo "Teste direkte Verbindung zu Bridge SMTP..."

{
    echo "HELO test.nk-it.cloud"
    sleep 1
    echo "MAIL FROM:<<EMAIL>>"
    sleep 1
    echo "RCPT TO:<<EMAIL>>"
    sleep 1
    echo "DATA"
    sleep 1
    echo "From: <EMAIL>"
    echo "To: <EMAIL>"
    echo "Subject: Direct Bridge Test"
    echo ""
    echo "Direct Bridge Test"
    echo "."
    sleep 1
    echo "QUIT"
} | nc "$BRIDGE_IP" 1025

echo ""
echo "🧪 Test 2: PMG Transport Test"
echo "============================="
echo "Teste ob PMG den Transport zur Bridge erreichen kann..."

# Test von PMG Container aus (falls SSH möglich)
if command -v ssh &> /dev/null; then
    echo "Versuche SSH zu PMG für Transport-Test..."
    ssh -o ConnectTimeout=5 root@"$PMG_IP" "nc -z $BRIDGE_IP 1025 && echo 'PMG kann Bridge erreichen' || echo 'PMG kann Bridge NICHT erreichen'" 2>/dev/null || echo "SSH zu PMG nicht möglich"
else
    echo "SSH nicht verfügbar für PMG Transport-Test"
fi

echo ""
echo "🧪 Test 3: Netzwerk-Routing"
echo "==========================="
echo "Teste Netzwerk-Routing zwischen PMG und Bridge..."

# Ping Test
echo "Ping PMG → Bridge:"
ping -c 3 "$BRIDGE_IP" | grep "packets transmitted" || echo "Ping fehlgeschlagen"

echo ""
echo "Traceroute PMG → Bridge:"
traceroute -m 5 "$BRIDGE_IP" 2>/dev/null | head -5 || echo "Traceroute nicht verfügbar"

echo ""
echo "🧪 Test 4: Port-Verfügbarkeit"
echo "============================="

# PMG Ports
echo "PMG Ports:"
for port in 25 8006; do
    if nc -z "$PMG_IP" "$port"; then
        echo "✅ PMG Port $port erreichbar"
    else
        echo "❌ PMG Port $port nicht erreichbar"
    fi
done

echo ""
echo "Bridge Ports:"
for port in 1025 1143 8080; do
    if nc -z "$BRIDGE_IP" "$port"; then
        echo "✅ Bridge Port $port erreichbar"
    else
        echo "❌ Bridge Port $port nicht erreichbar"
    fi
done

echo ""
echo "🧪 Test 5: Docker Bridge Status"
echo "==============================="
docker compose ps
echo ""
docker compose logs --tail=10 proton-bridge

echo ""
echo "🧪 Test 6: PMG Konfiguration Check"
echo "=================================="
echo "PMG sollte konfiguriert sein mit:"
echo "- Domain: nk-it.cloud"
echo "- Transport: $BRIDGE_IP:1025"
echo ""
echo "Prüfen Sie in PMG Web-Interface:"
echo "https://$PMG_IP:8006/#pmgMailProxyConfiguration:transports"

echo ""
echo "🔧 Lösungsvorschläge:"
echo "===================="
echo "1. Prüfen Sie PMG Transport-Konfiguration:"
echo "   - Domain: nk-it.cloud"
echo "   - Host: $BRIDGE_IP"
echo "   - Port: 1025"
echo ""
echo "2. Prüfen Sie PMG Relay-Domains:"
echo "   - nk-it.cloud sollte als Relay-Domain konfiguriert sein"
echo ""
echo "3. Prüfen Sie Netzwerk-Konnektivität:"
echo "   - PMG muss Bridge auf Port 1025 erreichen können"
echo ""
echo "4. Bridge Status prüfen:"
echo "   - docker compose logs proton-bridge"
echo ""
echo "5. PMG Logs prüfen:"
echo "   - SSH zu PMG: tail -f /var/log/mail.log"
