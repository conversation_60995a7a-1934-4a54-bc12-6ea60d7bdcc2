#!/bin/bash

# PMG → PROTON Bridge Simple Integration
# ======================================

source "$(dirname "$0")/.env"

echo "🔗 PMG → PROTON Bridge Integration"
echo "=================================="
echo "PMG: $PMG_IP (https://$PMG_IP:8006)"
echo "Bridge: $CONTAINER_IP"
echo "Domain: $PMG_DOMAIN"
echo ""

# Einfache Verbindungstests
echo "📊 Verbindungstests:"
echo "==================="

# PMG Web-Interface Test
echo "🔧 PMG Web-Interface:"
if nc -z "$PMG_IP" 8006; then
    echo "✅ PMG Port 8006 erreichbar"
    echo "   Web-Interface: https://$PMG_IP:8006"
else
    echo "❌ PMG Port 8006 nicht erreichbar"
fi

# PMG SMTP Test
echo ""
echo "📧 PMG SMTP:"
if nc -z "$PMG_IP" "$PMG_INTERNAL_PORT"; then
    echo "✅ PMG SMTP Port $PMG_INTERNAL_PORT erreichbar"
else
    echo "❌ PMG SMTP Port $PMG_INTERNAL_PORT nicht erreichbar"
fi

# Bridge Tests
echo ""
echo "🌉 PROTON Bridge:"
if nc -z "$CONTAINER_IP" "$BRIDGE_SMTP_PORT"; then
    echo "✅ Bridge SMTP ($CONTAINER_IP:$BRIDGE_SMTP_PORT) erreichbar"
else
    echo "❌ Bridge SMTP nicht erreichbar"
fi

if nc -z "$CONTAINER_IP" "$BRIDGE_IMAP_PORT"; then
    echo "✅ Bridge IMAP ($CONTAINER_IP:$BRIDGE_IMAP_PORT) erreichbar"
else
    echo "❌ Bridge IMAP nicht erreichbar"
fi

# Mail-Flow Übersicht
echo ""
echo "📋 Konfiguration:"
echo "================="
echo "1. PMG Transport bereits konfiguriert:"
echo "   Domain: $PMG_DOMAIN"
echo "   Transport: $PMG_TRANSPORT_HOST:$PMG_TRANSPORT_PORT"
echo ""
echo "2. Mail-Client Einstellungen:"
echo "   IMAP Server: $PMG_IP:143"
echo "   SMTP Server: $PMG_IP:$PMG_INTERNAL_PORT"
echo "   Username: <EMAIL>"
echo "   Password: a4a_n2Ri1At22FsQ9jjPFA"
echo ""
echo "3. Mail-Flow:"
echo "   Client → PMG ($PMG_IP) → Bridge ($CONTAINER_IP) → PROTON"

# Docker Status
echo ""
echo "🐳 Docker Status:"
docker compose ps

echo ""
echo "✅ PMG Integration bereit!"
echo "📧 Stellen Sie Ihre Mail-Clients auf PMG ($PMG_IP) um"
echo "🌐 PMG Web-Interface: https://$PMG_IP:8006"
